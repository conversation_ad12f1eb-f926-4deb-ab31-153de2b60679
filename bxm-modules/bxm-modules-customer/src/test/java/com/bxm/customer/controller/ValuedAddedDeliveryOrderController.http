### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的upsert方法
### 基于表单界面数据构建的真实测试场景

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImVlMjI4YmUyLTViN2ItNGM2MS1iOTFmLTVjZjlmOWQ3NTFkMSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.MeIdEI-oh2kS4az195xbwscNjt02jlfLFuxeEpN23ZL2vW4R9O4OfeeiRteJGEP1O-8OA-g9B0HH0zVFfg4d8w
### ========================================
### 1. 正常新增场景测试 - 小规模纳税人 + 税务/国税
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430A1B",
  "customerName": "测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "张三",
  "contactMobile": "***********",
  "contactIdNumber": "350105199*********",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "请按账期提供银行流水（对账单和回单）、进销票、补账期间的个税明细报表明细的三大报表、余额表、固定资产明细表、无形资产明细表、库存表支持jpg、png、pdf、word、xls",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "orgId": 1,
  "status": "DRAFT"
}

### ========================================
### 2. 正常新增场景测试 - 一般纳税人 + 社医保
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051431C2D",
  "customerName": "一般纳税人测试公司",
  "creditCode": "91***************X",
  "taxNo": "***************",
  "taxpayerType": 2,
  "valueAddedItemType": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "李四",
  "contactMobile": "***********",
  "contactIdNumber": "110000199002022345",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "社医保相关材料提交要求",
  "syncReassignment": true,
  "modifyDueDate": false,
  "ddl": "2025-06-30",
  "orgId": 2,
  "status": "PENDING_CONFIRMATION"
}

### ========================================
### 3. 正常更新场景测试 - 包含ID的更新操作
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "VAD2508051432E3F",
  "customerName": "更新测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "王五",
  "contactMobile": "***********",
  "contactIdNumber": "350105199003033456",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "SPECIAL_INDUSTRY"]
  },
  "requirements": "更新后的交付要求",
  "syncReassignment": false,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "orgId": 1,
  "status": "DELIVERY_COMPLETED"
}

### ========================================
### 4. 参数验证测试 - 客户名称为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051433G4H",
  "customerName": "",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 5. 参数验证测试 - 信用代码格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051434I5J",
  "customerName": "格式错误测试公司",
  "creditCode": "invalid_credit_code",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 6. 参数验证测试 - 纳税性质超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030005",
  "customerName": "纳税性质错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 5,
  "valueAddedItemType": 1
}

### ========================================
### 7. 参数验证测试 - 增值事项超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030006",
  "customerName": "增值事项错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 10
}

### ========================================
### 8. 边界条件测试 - 最小有效数据
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030007",
  "customerName": "最小数据测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 9. 边界条件测试 - 最大长度字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030008",
  "customerName": "这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1,
  "requirements": "这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制"
}

### ========================================
### 10. 完整业务场景测试 - 个税账号类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030009",
  "customerName": "个税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemType": 4,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "赵六",
  "contactMobile": "***********",
  "contactIdNumber": "350105199004044567",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "个税账号相关材料要求",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-03-31",
  "orgId": 3,
  "status": "SUBMITTED_PENDING_DELIVERY"
}

### ========================================
### 11. 参数验证测试 - 账期开始时间晚于结束时间
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508030010",
  "customerName": "账期验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1,
  "accountingPeriodStart": 202512,
  "accountingPeriodEnd": 202501
}

### ========================================
### 12. 测试生成交付单编号接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Authorization: {{authorization}}

### ========================================
### 13. 测试根据交付单编号查询接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2501271430123
Authorization: {{authorization}}

### ========================================
### 14. "改账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050001",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "改账",
  "customId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "改账联系人",
  "contactMobile": "***********",
  "contactIdNumber": "350105199*********",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "改账场景测试：需要根据客户服务ID更新相关字段信息",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "orgId": 1,
  "status": "DRAFT"
}

### ========================================
### 15. "改账"场景测试 - customId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050002",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "改账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "改账联系人",
  "contactMobile": "***********"
}

### ========================================
### 16. "改账"场景测试 - customId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050003",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "改账",
  "customId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "改账联系人",
  "contactMobile": "***********"
}

### ========================================
### 17. "补账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050004",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "补账联系人",
  "contactMobile": "***********",
  "contactIdNumber": "350105199002022345",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账场景测试：自动补全202501-202512期间缺失的账期记录",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "orgId": 1,
  "status": "DRAFT"
}

### ========================================
### 18. "补账"场景测试 - customerId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050005",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "补账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "补账联系人",
  "contactMobile": "***********"
}

### ========================================
### 19. "补账"场景测试 - 账期格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050006",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202513,
  "accountingPeriodEnd": 202501,
  "contactName": "补账联系人",
  "contactMobile": "***********"
}

### ========================================
### 20. "补账"场景测试 - customerId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050007",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "补账",
  "customerId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "补账联系人",
  "contactMobile": "***********"
}

### ========================================
### 21. 边界值测试 - itemName为其他值（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050008",
  "customerName": "普通增值服务测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "itemName": "其他服务",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "普通联系人",
  "contactMobile": "***********"
}

### ========================================
### 22. 边界值测试 - itemName为空（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050009",
  "customerName": "无itemName测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "普通联系人",
  "contactMobile": "***********"
}

### ========================================
### 23. 综合场景测试 - 改账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050010",
  "customerName": "改账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemType": 3,
  "itemName": "改账",
  "customId": 1,
  "customerId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactName": "改账综合联系人",
  "contactMobile": "***********",
  "contactIdNumber": "350105199005055678",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH"]
  },
  "requirements": "改账综合测试：包含所有字段的完整测试",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "orgId": 1,
  "status": "PENDING_CONFIRMATION"
}

### ========================================
### 24. 综合场景测试 - 补账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508050011",
  "customerName": "补账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemType": 4,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202401,
  "accountingPeriodEnd": 202412,
  "contactName": "补账综合联系人",
  "contactMobile": "***********",
  "contactIdNumber": "350105199006066789",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账综合测试：包含所有字段的完整测试，补全2024年全年账期",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-10-31",
  "orgId": 2,
  "status": "SUBMITTED_PENDING_DELIVERY"
}


